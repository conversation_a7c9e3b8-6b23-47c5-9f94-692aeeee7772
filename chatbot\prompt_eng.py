# Script 2: Customer Service Agent with Prompt Engineering (No RAG)
# Using prompt engineering to customize responses without knowledge base

import os
from llama_index.llms.gemini import Gemini

# Set up your API key
GOOGLE_API_KEY="AIzaSyB9UVxE45gsO8QVg_0qHDFR9JUfyNP09KA"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

# Initialize Gemini LLM
llm = Gemini(
    model="models/gemini-2.5-flash-preview-05-20",
    api_key=GOOGLE_API_KEY,  # uses GOOGLE_API_KEY env var by default
)
print("=== Customer Service Agent with Prompt Engineering===")
print("Hello! I'm your customer service agent for TechSupport Inc. How can I help you today?")





# Custom prompt template for customer service agent
def create_customer_service_prompt(user_question):
    prompt = f"""
            You are a helpful and professional customer service agent for Police Health Maintenance Limited (PHML Nigeria), an NHIA-accredited Health Maintenance Organization.

            Guidelines for your responses:

            Always be polite, professional, and empathetic

            Assume you work for PHML Nigeria, supporting enrollees under the NHIA scheme

            If you don’t have specific information, acknowledge this and offer to connect the user with the right department

            Provide helpful and accurate guidance where possible

            Use simple and clear language, especially for health-related questions

            End each response with an offer to assist further if needed

            Keep responses concise, informative, and reassuring

            Customer Question: {user_question}
            Please respond as a PHML customer service agent:
            """
    return prompt

  
# Example customer interactions
customer_question = "What are your business hours?"

prompt = create_customer_service_prompt(customer_question)
response = llm.complete(prompt)
print(f"Agent: {response}")


