# PHML Nigeria - Customer Service Agent with Advanced Prompt Engineering
# Streamlit interface with sophisticated prompt engineering for customer service

import os
import streamlit as st
from llama_index.llms.gemini import Gemini
from datetime import datetime

# Set up your API key
GOOGLE_API_KEY = "AIzaSyB9UVxE45gsO8QVg_0qHDFR9JUfyNP09KA"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

# Initialize Gemini LLM
@st.cache_resource
def initialize_llm():
    return Gemini(
        model="models/gemini-2.5-flash-preview-05-20",
        api_key=GOOGLE_API_KEY,
    )





# Custom prompt template for customer service agent
def create_customer_service_prompt(user_question, conversation_context=""):
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M")

    prompt = f"""
You are a helpful and professional customer service agent for Police Health Maintenance Limited (PHML Nigeria),
an NHIA-accredited Health Maintenance Organization serving healthcare needs across Nigeria.

COMPANY CONTEXT:
- PHML Nigeria is a leading HMO providing comprehensive healthcare services
- We serve NHIA enrollees and private clients
- Our mission is to provide accessible, quality healthcare to all Nigerians
- We have a network of healthcare providers across major Nigerian cities

RESPONSE GUIDELINES:
1. Always be polite, professional, and empathetic
2. Address the customer by acknowledging their concern first
3. Provide specific, actionable information when possible
4. If you don't have specific information, acknowledge this professionally and offer alternatives
5. Use simple, clear language that's easy to understand
6. Show empathy for health-related concerns
7. End each response with an offer to assist further
8. Maintain a warm, caring tone appropriate for healthcare services
9. Reference PHML Nigeria's commitment to quality healthcare

CONVERSATION CONTEXT:
{conversation_context}

CURRENT TIME: {current_time}

CUSTOMER QUESTION: {user_question}

Please respond as a PHML Nigeria customer service agent, keeping your response helpful, professional, and caring:
"""
    return prompt

# Streamlit page configuration
st.set_page_config(
    page_title="PHML Nigeria - Customer Service Agent",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for enhanced PHML Nigeria branding
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(135deg, #1e3c72, #2a5298);
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .main-header h1 {
        color: white;
        text-align: center;
        margin: 0;
        font-size: 2.5rem;
    }
    .main-header p {
        color: #e8f4fd;
        text-align: center;
        margin: 0.5rem 0 0 0;
        font-size: 1.2rem;
    }
    .agent-badge {
        background: linear-gradient(90deg, #4CAF50, #45a049);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        display: inline-block;
        margin-bottom: 1rem;
        font-weight: bold;
    }
    .service-info {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #2a5298;
        margin: 1rem 0;
    }
    .chat-stats {
        background: linear-gradient(90deg, #e3f2fd, #bbdefb);
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Header
st.markdown("""
<div class="main-header">
    <h1>🏥 PHML Nigeria Customer Service</h1>
    <p>Professional Healthcare Support with Advanced AI</p>
</div>
""", unsafe_allow_html=True)

# Agent badge
st.markdown("""
<div class="agent-badge">
    🤖 AI-Powered Customer Service Agent - Available 24/7
</div>
""", unsafe_allow_html=True)

# Sidebar with enhanced information
with st.sidebar:
    st.header("🏥 PHML Nigeria Services")

    st.markdown("""
    <div class="service-info">
    <h4>Our Healthcare Services:</h4>
    <ul>
        <li>🏥 Primary Healthcare</li>
        <li>🚑 Emergency Services</li>
        <li>💊 Pharmacy Services</li>
        <li>🔬 Laboratory Tests</li>
        <li>👨‍⚕️ Specialist Consultations</li>
        <li>🏥 Hospital Admissions</li>
        <li>👶 Maternal & Child Health</li>
        <li>💉 Preventive Care</li>
    </ul>
    </div>
    """, unsafe_allow_html=True)

    st.header("📞 Contact Information")
    st.markdown("""
    **Emergency Hotline**: 📞 0800-PHML-911
    **Customer Service**: 📞 +234-1-XXXX-XXX
    **Email**: <EMAIL>
    **Website**: www.phmlnigeria.com

    **Head Office**:
    📍 Victoria Island, Lagos, Nigeria

    **Regional Offices**:
    📍 Abuja • Port Harcourt • Kano • Ibadan
    """)

    st.header("⚙️ Chat Controls")
    if st.button("🔄 New Conversation", type="primary"):
        st.session_state.messages = []
        st.session_state.conversation_context = ""
        st.rerun()

    # Chat statistics
    if "messages" in st.session_state:
        msg_count = len([m for m in st.session_state.messages if m["role"] == "user"])
        st.markdown(f"""
        <div class="chat-stats">
        <strong>Session Stats:</strong><br>
        💬 Messages: {msg_count}<br>
        🕒 Started: {datetime.now().strftime("%H:%M")}
        </div>
        """, unsafe_allow_html=True)

# Initialize chat history and conversation context
if "messages" not in st.session_state:
    st.session_state.messages = [
        {
            "role": "assistant",
            "content": "Hello! Welcome to PHML Nigeria Customer Service. I'm your dedicated AI assistant, here to help you with all your healthcare and insurance inquiries. Whether you need information about our services, help with claims, or guidance on healthcare options, I'm here to assist you professionally and promptly. How may I help you today?"
        }
    ]

if "conversation_context" not in st.session_state:
    st.session_state.conversation_context = ""

# Initialize LLM
llm = initialize_llm()

# Display chat messages
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])
# Chat input
if prompt := st.chat_input("Type your question or concern here..."):
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": prompt})

    # Display user message
    with st.chat_message("user"):
        st.markdown(prompt)

    # Generate and display assistant response
    with st.chat_message("assistant"):
        with st.spinner("Our customer service agent is preparing your response..."):
            try:
                # Create enhanced prompt with conversation context
                enhanced_prompt = create_customer_service_prompt(
                    prompt,
                    st.session_state.conversation_context
                )

                response = llm.complete(enhanced_prompt)
                response_text = str(response)

                # Display the response
                st.markdown(response_text)

                # Add assistant response to chat history
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": response_text
                })

                # Update conversation context (keep last 3 exchanges)
                recent_messages = st.session_state.messages[-6:]  # Last 3 user-assistant pairs
                context_summary = "\n".join([
                    f"{msg['role'].title()}: {msg['content'][:100]}..."
                    for msg in recent_messages
                ])
                st.session_state.conversation_context = context_summary

            except Exception as e:
                error_message = f"I apologize for the technical difficulty. Please try again, or contact our customer service directly at 0800-PHML-911 for immediate assistance. Error details: {str(e)}"
                st.error(error_message)
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": error_message
                })

# Footer with additional information
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666; padding: 2rem;">
    <h4>🏥 PHML Nigeria - Your Trusted Healthcare Partner</h4>
    <p><strong>Available 24/7 for your healthcare needs</strong></p>
    <p>© 2024 Police Health Maintenance Limited (PHML Nigeria) - NHIA Accredited HMO</p>
    <p><em>⚠️ For medical emergencies, please call 199 or visit the nearest hospital immediately.</em></p>
    <p><em>This AI assistant provides general information and support. For specific medical advice, please consult with our healthcare professionals.</em></p>
</div>
""", unsafe_allow_html=True)
