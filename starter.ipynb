%pip install llama-index-llms-gemini llama-index

import os

GOOGLE_API_KEY="AIzaSyB9UVxE45gsO8QVg_0qHDFR9JUfyNP09KA"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

from llama_index.llms.gemini import Gemini

llm = Gemini(
    model="models/gemini-2.5-flash-preview-05-20",
    api_key=GOOGLE_API_KEY,  # uses GOOGLE_API_KEY env var by default
)

from llama_index.llms.gemini import Gemini

resp = llm.complete("Write a short story about the Nigerian Police Force.")
print(resp)

resp = llm.complete("Write a short story about the Nigerian Police Force. Give it a title.")
print(resp)

resp = llm.stream_complete("Write a short story about the Nigerian Army. Give it a title.")
for r in resp:
    print(r.text, end="")

resp = llm.stream_complete("Tell me about PHML in Nigeria")
for r in resp:
    print(r.text, end="")

resp = llm.complete("How can I register for PHML?")
print(resp)

