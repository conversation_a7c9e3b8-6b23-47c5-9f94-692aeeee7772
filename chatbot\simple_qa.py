# PHML Nigeria - Streamlit Chat Interface with Gemini
# Interactive chat application for customer service

import os
import streamlit as st
from llama_index.llms.gemini import Gemini

# Set up your API key (you'll need to get this from Google AI Studio)
GOOGLE_API_KEY = "AIzaSyB9UVxE45gsO8QVg_0qHDFR9JUfyNP09KA"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

# Initialize Gemini LLM
@st.cache_resource
def initialize_llm():
    return Gemini(
        model="models/gemini-2.5-flash-preview-05-20",
        api_key=GOOGLE_API_KEY,
    )

# Streamlit page configuration
st.set_page_config(
    page_title="PHML Nigeria - AI Assistant",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for PHML Nigeria branding
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #2E8B57, #228B22);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .main-header h1 {
        color: white;
        text-align: center;
        margin: 0;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .assistant-message {
        background-color: #f1f8e9;
        border-left: 4px solid #4caf50;
    }
</style>
""", unsafe_allow_html=True)

# Header
st.markdown("""
<div class="main-header">
    <h1>🏥 PHML Nigeria - AI Assistant</h1>
    <p style="text-align: center; color: white; margin: 0;">
        Your intelligent healthcare support system
    </p>
</div>
""", unsafe_allow_html=True)

# Sidebar
with st.sidebar:
    st.header("About PHML Nigeria")
    st.info("""
    **PHML Nigeria** is committed to providing excellent healthcare services.

    This AI assistant can help you with:
    - General inquiries
    - Service information
    - Support requests
    - Healthcare guidance
    """)

    st.header("Quick Actions")
    if st.button("🔄 Clear Chat History"):
        st.session_state.messages = []
        st.rerun()

    st.header("Contact Information")
    st.markdown("""
    📞 **Phone**: +234-XXX-XXXX
    📧 **Email**: <EMAIL>
    🌐 **Website**: www.phmlnigeria.com
    📍 **Address**: Lagos, Nigeria
    """)

# Initialize chat history
if "messages" not in st.session_state:
    st.session_state.messages = [
        {
            "role": "assistant",
            "content": "Hello! Welcome to PHML Nigeria. I'm your AI assistant. How can I help you today?"
        }
    ]

# Initialize LLM
llm = initialize_llm()

# Display chat messages
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Chat input
if prompt := st.chat_input("Type your message here..."):
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": prompt})

    # Display user message
    with st.chat_message("user"):
        st.markdown(prompt)

    # Generate and display assistant response
    with st.chat_message("assistant"):
        with st.spinner("Thinking..."):
            # Create context-aware prompt for PHML Nigeria
            context_prompt = f"""
            You are an AI assistant for PHML Nigeria, a healthcare organization in Nigeria.
            Please provide helpful, accurate, and professional responses related to healthcare services,
            general inquiries, and customer support. If you don't have specific information about
            PHML Nigeria's services, provide general helpful guidance while noting that the user
            should contact PHML Nigeria directly for specific details.

            User question: {prompt}
            """

            try:
                response = llm.complete(context_prompt)
                response_text = str(response)

                # Display the response
                st.markdown(response_text)

                # Add assistant response to chat history
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": response_text
                })

            except Exception as e:
                error_message = f"I apologize, but I'm experiencing technical difficulties. Please try again or contact PHML Nigeria directly. Error: {str(e)}"
                st.error(error_message)
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": error_message
                })

# Footer
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666; padding: 1rem;">
    <p>© 2024 PHML Nigeria - Powered by AI Technology</p>
    <p><em>This AI assistant is designed to provide general information and support.
    For medical emergencies, please contact emergency services immediately.</em></p>
</div>
""", unsafe_allow_html=True)