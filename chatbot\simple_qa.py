# Script 1: Simple QA Implementation with Gemini (No RAG)
# Basic demonstration of using Gemini for question answering

import os
from llama_index.llms.gemini import Gemini

# Set up your API key (you'll need to get this from Google AI Studio)

GOOGLE_API_KEY="AIzaSyB9UVxE45gsO8QVg_0qHDFR9JUfyNP09KA"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY
# Initialize Gemini LLM
llm = Gemini(
    model="models/gemini-2.5-flash-preview-05-20",
    api_key=GOOGLE_API_KEY,  # uses GOOGLE_API_KEY env var by default
)


print("=== Simple QA with (No Knowledge Base) ===")
print("Ask questions and see how the model responds with its training data only!")

# Example customer service questions
question = "What are your business hours?",
    # "How do I contact customer support?",

# for question in questions:
print(f"\nQuestion: {question}")
response = llm.complete(question)
print(f"Answer: {response}")


# Example customer service questions
question = "How do I contact customer support?"

# for question in questions:
print(f"\nQuestion: {question}")
response = llm.complete(question)
print(f"Answer: {response}")