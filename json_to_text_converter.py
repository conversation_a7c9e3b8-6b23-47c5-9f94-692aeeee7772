#!/usr/bin/env python3
"""
Convert PHML JSON data to formatted plain text and PDF files
"""

import json
import os
from datetime import datetime
from typing import Dict, Any

class PHMLDataFormatter:
    def __init__(self, json_file_path: str):
        self.json_file_path = json_file_path
        self.data = self.load_json_data()
        
    def load_json_data(self) -> Dict[str, Any]:
        """Load JSON data from file"""
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Error: JSON file not found at {self.json_file_path}")
            return {}
        except json.JSONDecodeError:
            print(f"Error: Invalid JSON in {self.json_file_path}")
            return {}
    
    def format_to_text(self) -> str:
        """Format JSON data to readable plain text"""
        if not self.data:
            return "No data available."
        
        text_content = []
        
        # Header
        text_content.append("=" * 80)
        text_content.append("POLICE HEALTH MAINTENANCE LIMITED (PHML) NIGERIA")
        text_content.append("COMPREHENSIVE WEBSITE DATA EXTRACTION")
        text_content.append("=" * 80)
        text_content.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        text_content.append(f"Source: {self.data.get('metadata', {}).get('base_url', 'Unknown')}")
        text_content.append("")
        
        # Table of Contents
        text_content.append("TABLE OF CONTENTS")
        text_content.append("-" * 40)
        text_content.append("1. Company Information")
        text_content.append("2. Contact Information")
        text_content.append("3. Board Members")
        text_content.append("4. Management Team")
        text_content.append("5. Services Offered")
        text_content.append("6. Departments")
        text_content.append("7. Frequently Asked Questions")
        text_content.append("8. Downloads Available")
        text_content.append("9. Training Data Content")
        text_content.append("10. Metadata")
        text_content.append("")
        
        # 1. Company Information
        text_content.append("1. COMPANY INFORMATION")
        text_content.append("=" * 50)
        company_info = self.data.get('company_info', {})
        
        if company_info.get('mission'):
            text_content.append("MISSION:")
            text_content.append(self.wrap_text(company_info['mission']))
            text_content.append("")
        
        if company_info.get('vision'):
            text_content.append("VISION:")
            text_content.append(self.wrap_text(company_info['vision']))
            text_content.append("")
        
        if company_info.get('goal'):
            text_content.append("GOAL:")
            text_content.append(self.wrap_text(company_info['goal']))
            text_content.append("")
        
        if company_info.get('values'):
            text_content.append("COMPANY VALUES:")
            for i, value in enumerate(company_info['values'], 1):
                title = value.get('title', f'Value {i}')
                description = value.get('description', 'No description')
                text_content.append(f"{i}. {title}")
                text_content.append(f"   {self.wrap_text(description, indent='   ')}")
                text_content.append("")
        
        if company_info.get('about_details'):
            text_content.append("ABOUT DETAILS:")
            for detail in company_info['about_details']:
                text_content.append(f"• {self.wrap_text(detail, indent='  ')}")
            text_content.append("")
        
        # 2. Contact Information
        text_content.append("2. CONTACT INFORMATION")
        text_content.append("=" * 50)
        contact_info = self.data.get('contact_info', {})
        
        if contact_info.get('address'):
            text_content.append(f"ADDRESS: {contact_info['address']}")
            text_content.append("")
        
        if contact_info.get('phones'):
            text_content.append("PHONE NUMBERS:")
            for phone in contact_info['phones']:
                text_content.append(f"  • {phone}")
            text_content.append("")
        
        if contact_info.get('emails'):
            text_content.append("EMAIL ADDRESSES:")
            for email in contact_info['emails']:
                text_content.append(f"  • {email}")
            text_content.append("")
        
        if contact_info.get('social_media'):
            text_content.append("SOCIAL MEDIA:")
            for platform, url in contact_info['social_media'].items():
                text_content.append(f"  • {platform.title()}: {url}")
            text_content.append("")
        
        # 3. Board Members
        text_content.append("3. BOARD MEMBERS")
        text_content.append("=" * 50)
        board_members = self.data.get('board_members', [])
        
        if board_members:
            # Remove duplicates
            unique_members = []
            seen_names = set()
            for member in board_members:
                name = member.get('name', '')
                if name and name not in seen_names:
                    unique_members.append(member)
                    seen_names.add(name)
            
            for i, member in enumerate(unique_members, 1):
                name = member.get('name', 'Unknown')
                position = member.get('position', 'Unknown Position')
                text_content.append(f"{i}. {name}")
                text_content.append(f"   Position: {position}")
                text_content.append("")
        else:
            text_content.append("No board member information available.")
            text_content.append("")
        
        # 4. Management Team
        text_content.append("4. MANAGEMENT TEAM")
        text_content.append("=" * 50)
        mgmt_team = self.data.get('management_team', [])
        
        if mgmt_team:
            text_content.append("Note: Management team member names may need to be extracted from images.")
            text_content.append(f"Total management team members identified: {len(mgmt_team)}")
            text_content.append("")
            
            # Show image URLs for reference
            unique_images = list(set(member.get('image_url', '') for member in mgmt_team if member.get('image_url')))
            text_content.append("Management team image references:")
            for i, img_url in enumerate(unique_images, 1):
                text_content.append(f"  {i}. {img_url}")
            text_content.append("")
        else:
            text_content.append("No management team information available.")
            text_content.append("")
        
        # 5. Services Offered
        text_content.append("5. SERVICES OFFERED")
        text_content.append("=" * 50)
        services = self.data.get('services', [])
        
        if services:
            # Remove duplicates and filter meaningful services
            unique_services = []
            seen_descriptions = set()
            for service in services:
                desc = service.get('description', '')
                if desc and len(desc) > 50 and desc not in seen_descriptions:
                    unique_services.append(service)
                    seen_descriptions.add(desc)
            
            for i, service in enumerate(unique_services, 1):
                title = service.get('title', f'Service {i}')
                description = service.get('description', 'No description')
                source = service.get('source', 'main_page')
                
                text_content.append(f"{i}. {title}")
                text_content.append(f"   {self.wrap_text(description, indent='   ')}")
                text_content.append(f"   Source: {source}")
                text_content.append("")
        else:
            text_content.append("No services information available.")
            text_content.append("")
        
        return "\n".join(text_content)
    
    def wrap_text(self, text: str, width: int = 75, indent: str = "") -> str:
        """Wrap text to specified width with optional indentation"""
        if not text:
            return ""
        
        words = text.split()
        lines = []
        current_line = indent
        
        for word in words:
            if len(current_line + word) <= width:
                current_line += word + " "
            else:
                if current_line.strip():
                    lines.append(current_line.rstrip())
                current_line = indent + word + " "
        
        if current_line.strip():
            lines.append(current_line.rstrip())
        
        return "\n".join(lines)
    
    def save_to_text_file(self, output_path: str = "phml_data/phml_formatted_data.txt"):
        """Save formatted data to text file"""
        formatted_text = self.format_to_text()
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(formatted_text)
            print(f"✅ Text file saved: {output_path}")
            return True
        except Exception as e:
            print(f"❌ Error saving text file: {str(e)}")
            return False
    
    def save_to_pdf(self, output_path: str = "phml_data/phml_formatted_data.pdf"):
        """Save formatted data to PDF file"""
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            
            # Create PDF document
            doc = SimpleDocTemplate(output_path, pagesize=A4,
                                  rightMargin=72, leftMargin=72,
                                  topMargin=72, bottomMargin=18)
            
            # Get styles
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1  # Center alignment
            )
            
            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                spaceBefore=12
            )
            
            normal_style = styles['Normal']
            normal_style.fontSize = 10
            normal_style.spaceAfter = 6
            
            # Build PDF content
            story = []
            
            # Title
            story.append(Paragraph("POLICE HEALTH MAINTENANCE LIMITED (PHML) NIGERIA", title_style))
            story.append(Paragraph("COMPREHENSIVE WEBSITE DATA EXTRACTION", title_style))
            story.append(Spacer(1, 12))
            
            # Get formatted text and convert to PDF paragraphs
            formatted_text = self.format_to_text()
            lines = formatted_text.split('\n')
            
            for line in lines:
                if line.startswith('='):
                    continue  # Skip separator lines
                elif line.isupper() and len(line) > 10:
                    story.append(Paragraph(line, heading_style))
                elif line.strip():
                    # Escape HTML characters and handle special formatting
                    escaped_line = line.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                    story.append(Paragraph(escaped_line, normal_style))
                else:
                    story.append(Spacer(1, 6))
            
            # Build PDF
            doc.build(story)
            print(f"✅ PDF file saved: {output_path}")
            return True
            
        except ImportError:
            print("❌ ReportLab not installed. Installing...")
            try:
                import subprocess
                subprocess.check_call(['pip', 'install', 'reportlab'])
                print("✅ ReportLab installed. Please run the script again.")
                return False
            except Exception as e:
                print(f"❌ Failed to install ReportLab: {str(e)}")
                print("💡 Please install manually: pip install reportlab")
                return False
        except Exception as e:
            print(f"❌ Error creating PDF: {str(e)}")
            return False

def main():
    """Main function to convert JSON to text and PDF"""
    json_file = "phml_data/phml_complete_data.json"
    
    if not os.path.exists(json_file):
        print(f"❌ JSON file not found: {json_file}")
        print("💡 Please run the scraper first to generate the JSON data.")
        return
    
    print("🔄 Converting PHML JSON data to formatted files...")
    
    formatter = PHMLDataFormatter(json_file)
    
    # Create text file
    text_success = formatter.save_to_text_file()
    
    # Create PDF file
    pdf_success = formatter.save_to_pdf()
    
    print("\n" + "="*60)
    print("CONVERSION SUMMARY")
    print("="*60)
    print(f"📄 Text file: {'✅ Created' if text_success else '❌ Failed'}")
    print(f"📑 PDF file: {'✅ Created' if pdf_success else '❌ Failed'}")
    
    if text_success or pdf_success:
        print("\n💡 Files saved in the 'phml_data' directory")
        print("📖 The formatted files contain all extracted website data in readable format")

if __name__ == "__main__":
    main()
