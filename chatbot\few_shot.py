# Script 3: Customer Service Agent with Few-Shot Examples (No RAG)
# Enhanced with sample FAQ responses for better context understanding

import os
from llama_index.llms.gemini import Gemini

# Set up your API key
os.environ["GOOGLE_API_KEY"] = "your_gemini_api_key_here"

# Initialize Gemini LLM
llm = Gemini(model="models/gemini-pro")

# Enhanced prompt template with few-shot examples
def create_few_shot_prompt(user_question):
    prompt = f"""
You are a professional customer service agent for TechSupport Inc., a business software company.
Use the following examples to understand how to respond to customer inquiries:

EXAMPLE RESPONSES:

Customer: "How do I register under the NHIA scheme?"
Agent: "I'd be happy to guide you through the registration process! Here’s how it usually works:
1. Obtain and fill out the NHIA enrollment form (you can request this at any PHML office or designated NHIA centers)
2. Submit the form along with required documents like your ID, passport photograph, and, if applicable, a pay slip or attestation letter
3. Once submitted, you'll receive a unique NHIA number when approved
If you need help locating the nearest office or downloading the forms, I can assist with that. Would you like me to send you a link or contact details?"

Customer: "I was asked to pay for drugs at the hospital. Why?"
Agent: "I'm sorry to hear that you were asked to pay. Under NHIA, most essential drugs are covered, but a 10% co-payment is required in many cases.
However, if you were charged more than that, or for items that should be covered, it may need to be investigated.
Could you share the hospital name and details of the charges? I can escalate this to our medical services or complaints team for follow-up."

Customer: "I want to change my hospital."
Agent: "Absolutely, you're allowed to change your hospital under NHIA. Here's how it works:
1. Visit any PHML office or request a 'Change of Provider' form
2. Fill it out with your NHIA number and preferred new facility
3. Submit it—changes typically take effect the following month
Would you like me to send you the form or guide you to the nearest office to complete the process?"

Customer: "How do I get a referral for specialist care?"
Agent: "Referrals are issued by your primary healthcare provider when they determine that specialist care is necessary.
The hospital will usually send a referral request to PHML electronically or via a form. Once reviewed, a referral code is issued and sent to the facility.
If you believe your referral is delayed, I can help you track it or contact the medical review team. Shall I assist with that?"

Customer: "What services are covered under NHIA?"
Agent: "NHIA provides a wide range of healthcare benefits, including:
- Outpatient consultation
- Maternity care
- Immunizations
- Essential drugs and diagnostic tests
- Specialist care via referral
There are also exclusions (like some cosmetic surgeries or fertility treatments). I can send you a full breakdown of covered services if you'd like!"

Customer: "Where is your office located?"
Agent: "Our head office is located at [insert address, e.g., PHML House, Wuse Zone 3, Abuja].
We also have zonal offices across the country. If you tell me your current location or state, I can help you find the nearest one. Would you like me to do that?
Now please respond to this customer inquiry in a similar style:

Customer: {user_question}
Agent: """
    return prompt

print("=== Advanced Customer Service Agent with Few-Shot Learning (No RAG) ===")
print("Welcome to TechSupport Inc.! I'm here to help with any questions or concerns.")

# Test questions that should trigger few-shot example patterns
test_questions = [
    "I can't remember my login password",
    "This software doesn't work, I want my money back", 
    "The program won't install on my computer",
    "When can I call for support?",
    "How do I track my order?",
    "What payment options do you have?",
    "I need help with billing issues"
]

for question in test_questions:
    print(f"\n{'='*60}")
    print(f"Customer: {question}")
    print(f"{'='*60}")
    prompt = create_few_shot_prompt(question)
    response = llm.complete(prompt)
    print(f"Agent: {response}")

# Interactive mode (uncomment to enable)
# print(f"\n{'='*60}")
# print("=== Interactive Customer Service Session ===")
# print("Type 'quit' to end the session")
# 
# while True:
#     customer_input = input(f"\nCustomer: ")
#     if customer_input.lower() in ['quit', 'exit', 'goodbye', 'bye']:
#         print("Agent: Thank you for choosing TechSupport Inc.! Have a wonderful day!")
#         break
#     
#     prompt = create_few_shot_prompt(customer_input)
#     response = llm.complete(prompt)
#     print(f"Agent: {response}")

print(f"\n{'='*60}")
print("OBSERVATIONS:")
print("✓ Better structured responses due to few-shot examples")
print("✓ More consistent tone and helpfulness")
print("✗ Still no access to actual company data (hours, policies, etc.)")
print("✗ Cannot provide specific account or order information")
print("✗ Has to give generic responses or admit lack of knowledge")
print("\nThis is where RAG becomes essential - to provide real company knowledge!")