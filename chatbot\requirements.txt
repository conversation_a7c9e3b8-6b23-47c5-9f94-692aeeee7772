# PHML Nigeria - Streamlit Chat Application Requirements
# Core dependencies for the AI-powered healthcare chat interface

# Streamlit - Web application framework for the chat interface
streamlit>=1.28.0

# LlamaIndex - LLM integration framework
llama-index>=0.9.0

# Google Gemini integration for AI responses
llama-index-llms-gemini>=0.1.0

# Google Generative AI (required for Gemini)
google-generativeai>=0.3.0

# Web scraping dependencies (existing)
requests>=2.28.0
beautifulsoup4>=4.11.0
lxml>=4.9.0

# Data processing (recommended for better performance)
pandas>=1.5.0
numpy>=1.24.0

# Environment management (for secure API key handling)
python-dotenv>=1.0.0
